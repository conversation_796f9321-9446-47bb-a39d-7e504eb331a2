apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: homestead-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: homestead-deployment
  labels:
    app: homestead
spec:
  replicas: 2 # Start with 1 replica; scale up as needed
  selector:
    matchLabels:
      app: homestead
  template:
    metadata:
      labels:
        app: homestead
    spec:
      containers:
        - name: homestead
          image: docker.io/007lock/homestead-app:latest
          ports:
            - containerPort: 8011 # The internal port your application listens on
          env:
            - name: STORAGE_STOCKPRICES
              value: "/data"
            - name: ENVIRONMENT
              value: "production"
            - name: TELEGRAM_OWNER
              value: "816817223"
          envFrom:
            - secretRef:
                name: homestead-secrets
          volumeMounts:
            - name: homestead-data # Name of the volume mount
              mountPath: /data # Path inside the container
          resources: # <--- Start of resource definition
            requests:
              cpu: "100m"  # Request 0.1 CPU core
              memory: "256Mi" # Request 256 MiB of memory
            limits:
              cpu: "500m"  # Limit to 0.5 CPU core
              memory: "512Mi" # Limit to 512 MiB of memory
              
          readinessProbe: # Is the pod ready to accept traffic?
            httpGet:
              path: /health  # The endpoint to check
              port: 8011     # The port inside the container
            initialDelaySeconds: 5  # Wait 5s after container starts before first probe
            periodSeconds: 10       # Check every 10 seconds
            failureThreshold: 3     # Consider it failed after 3 consecutive failures

          livenessProbe: # Is the pod still alive and responsive?
            httpGet:
              path: /health  # Use the same endpoint
              port: 8011
            initialDelaySeconds: 15 # Wait longer before checking liveness
            periodSeconds: 20       # Check less frequently than readiness
            failureThreshold: 3     # After 3 failures, restart the container
      volumes:
        - name: homestead-data
          persistentVolumeClaim:
            claimName: homestead-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: homestead-service
  labels:
    app: homestead
spec:
  selector:
    app: homestead # Selects pods with label app: homestead (i.e., homestead-deployment pods)
  ports:
    - protocol: TCP
      port: 80 # The port you want to expose externally (host port in Docker Compose)
      targetPort: 8011 # The port your application is listening on inside the container
  type: NodePort # Or NodePort if you prefer.