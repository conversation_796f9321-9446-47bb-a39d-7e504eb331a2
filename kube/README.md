
# Run minikube with tailscale domain
```shell
minikube start --apiserver-names=minikube.tailb0d0ae.ts.net
```

# Cloudflare tunnel with kube

https://developers.cloudflare.com/cloudflare-one/tutorials/many-cfd-one-tunnel/

```shell
cloudflared tunnel login
cloudflared tunnel create example-tunnel
kubectl create secret generic tunnel-credentials \
--from-file=credentials.json=/Users/<USER>/.cloudflared/ef824aef-7557-4b41-a398-4684585177ad.json
```

- Go to the Cloudflare dashboard.
- Go to the DNS tab.
- Now create a CNAME targeting .cfargotunnel.com. In this example, the tunnel ID is ef824aef-7557-4b41-a398-4684585177ad, so create a CNAME record specifically targeting ef824aef-7557-4b41-a398-4684585177ad.cfargotunnel.com.
- Then run command to create deployment
```shell
kubectl apply -f cloudflared.yaml
```