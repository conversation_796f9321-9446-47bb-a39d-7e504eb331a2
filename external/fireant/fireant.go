package fireant

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"sort"
	"time"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/internal/contract"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/corpix/uarand"
)

type fireAntService struct {
	cfg       *config.Config
	transport *http.Transport
}

func NewFireAntService(cfg *config.Config, trans *http.Transport) contract.FireAnt {
	return &fireAntService{cfg, trans}
}

func (fa *fireAntService) FetchVNIStockPrices(ctx context.Context, page int) error {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: fa.transport,
	}
	ua := uarand.GetRandom()

	// build date
	now := time.Now()
	prevYear := now.AddDate(-3, 0, 0)
	// Crawl info to build url
	originUrl := "https://fireant.vn/"
	infoUrl := fmt.Sprintf("https://restv2.fireant.vn/symbols/VNINDEX/historical-quotes?startDate=%s&endDate=%s&offset=%d&limit=20", prevYear.Format("2006-01-02"), now.Format("2006-01-02"), page*20)
	req, err := http.NewRequest("GET", infoUrl, nil)
	if err != nil {
		return err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	if fa.cfg.FireAnt != nil && fa.cfg.FireAnt.BearerToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", fa.cfg.FireAnt.BearerToken))
	}
	req.Header.Set("origin", originUrl)
	req.Header.Set("referer", originUrl)
	req.Header.Set("Content-Type", "application/json, text/plain, */*")

	response, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return constants.RequestError.SHOULD_RETRY
	}
	defer response.Body.Close()

	var stockResp []*model.FireAntVNIPrices
	err = json.NewDecoder(response.Body).Decode(&stockResp)
	if err != nil {
		return err
	}

	stockFilePath := fmt.Sprintf("%s/VNINDEX.csv", fa.cfg.StoragePath.StockPrices)
	records := make(map[string][]string)
	if _, err = os.Stat(stockFilePath); err == nil {
		csvIn, err := os.Open(stockFilePath)
		if err != nil {
			return err
		}
		reader := csv.NewReader(csvIn)
		for {
			rec, err := reader.Read()
			if err == io.EOF {
				break
			}
			if err != nil {
				return err
			}
			if rec[0] == "date" {
				continue
			}
			records[rec[0]] = rec
		}
		csvIn.Close()
	}
	csvOut, err := os.OpenFile(stockFilePath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0755)
	if err != nil {
		return err
	}

	writer := csv.NewWriter(csvOut)
	defer csvOut.Close()
	header := []string{
		"date", "open", "high", "low", "close", "volume",
	}
	err = writer.Write(header)
	if err != nil {
		return err
	}

	for _, s := range stockResp {
		var record []string
		t, err := time.Parse("2006-01-02T15:04:05", s.Date)

		if err != nil {
			return err
		}
		date := fmt.Sprintf("%d", t.Unix())

		record = append(record,
			date,
			fmt.Sprintf("%.0f", s.PriceOpen),
			fmt.Sprintf("%.0f", s.PriceHigh),
			fmt.Sprintf("%.0f", s.PriceLow),
			fmt.Sprintf("%.0f", s.PriceClose),
			fmt.Sprintf("%.0f", s.TotalValue))

		records[date] = record
	}
	keys := make([]string, 0)
	for k := range records {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[j] < keys[i]
	})
	for _, k := range keys {
		err = writer.Write(records[k])
		if err != nil {
			return err
		}
	}
	writer.Flush()
	return nil
}

func (fa *fireAntService) FetchStockPrices(ctx context.Context, stockCode string, startDate string, endDate string, page int) (*model.FireAntStockPrices, error) {
	client := &http.Client{
		Timeout:   60 * time.Second,
		Transport: fa.transport,
	}
	ua := uarand.GetRandom()

	// Build URL with parameters
	originUrl := "https://fireant.vn/"
	offset := page * 20
	infoUrl := fmt.Sprintf("https://restv2.fireant.vn/symbols/%s/historical-quotes?startDate=%s&endDate=%s&offset=%d&limit=20", stockCode, startDate, endDate, offset)

	req, err := http.NewRequest("GET", infoUrl, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	req.Header.Set("User-Agent", ua)
	if fa.cfg.FireAnt != nil && fa.cfg.FireAnt.BearerToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", fa.cfg.FireAnt.BearerToken))
	}
	req.Header.Set("origin", originUrl)
	req.Header.Set("referer", originUrl)
	req.Header.Set("Content-Type", "application/json, text/plain, */*")

	response, err := client.Do(req)
	var e net.Error
	if errors.As(err, &e) && e.Timeout() {
		return nil, constants.RequestError.SHOULD_RETRY
	}
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	var stockResp model.FireAntStockPrices
	err = json.NewDecoder(response.Body).Decode(&stockResp)
	if err != nil {
		return nil, err
	}

	return &stockResp, nil
}
