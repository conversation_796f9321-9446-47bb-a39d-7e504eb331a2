package test

import (
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/007lock/simon-homestead/external/fireant"
	"github.com/007lock/simon-homestead/pkg/api/bot/stock"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/007lock/simon-homestead/pkg/util"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFetchStockPriceCache_Integration(t *testing.T) {
	// Skip if no bearer token is provided
	bearerToken := os.Getenv("FIREANT_BEARER_TOKEN")
	if bearerToken == "" {
		t.Skip("FIREANT_BEARER_TOKEN environment variable not set, skipping integration test")
	}

	// Setup configuration
	cfg := &config.Config{
		FireAnt: &config.FireAnt{
			BearerToken: bearerToken,
		},
	}

	// Setup HTTP transport
	transport := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
	}

	// Create FireAnt service
	faService := fireant.NewFireAntService(cfg, transport)

	// Create service with FireAnt
	s := &service.Service{
		FireAnt:   faService,
		Validator: util.NewValidator(),
	}

	// Setup Echo
	e := echo.New()
	e.Validator = s.Validator

	// Clean up test cache after test
	defer os.RemoveAll("test_cache")

	// Create handler
	handler := stock.FetchStockPriceCache(s)

	t.Run("should download and cache stock prices", func(t *testing.T) {
		// Create request
		req := httptest.NewRequest(http.MethodGet, "/stock/fetch-price/cache/VND", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("stockCode")
		c.SetParamValues("VND")

		// Execute handler
		err := handler(c)
		require.NoError(t, err)

		// Check response
		assert.Equal(t, http.StatusOK, rec.Code)
		assert.Contains(t, rec.Body.String(), "downloaded and cached successfully")

		// Check if cache file was created
		cacheFile := filepath.Join("precache/stockprices", "VND.csv")
		_, err = os.Stat(cacheFile)
		assert.NoError(t, err, "Cache file should be created")

		// Clean up
		os.Remove(cacheFile)
	})

	t.Run("should return cached message when file is recent", func(t *testing.T) {
		// Create cache directory and file
		cacheDir := "precache/stockprices"
		os.MkdirAll(cacheDir, 0755)
		cacheFile := filepath.Join(cacheDir, "VND.csv")

		// Create a recent cache file
		file, err := os.Create(cacheFile)
		require.NoError(t, err)
		file.WriteString("Date,Open,High,Low,Close,Volume\n")
		file.WriteString("2025-06-24,100,105,95,102,1000\n")
		file.Close()

		defer os.Remove(cacheFile) // Clean up

		// Create request
		req := httptest.NewRequest(http.MethodGet, "/stock/fetch-price/cache/VND", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("stockCode")
		c.SetParamValues("VND")

		// Execute handler
		err = handler(c)
		require.NoError(t, err)

		// Check response
		assert.Equal(t, http.StatusOK, rec.Code)
		assert.Contains(t, rec.Body.String(), "already cached and up to date")
	})

	t.Run("should return error for empty stock code", func(t *testing.T) {
		// Create request without stock code
		req := httptest.NewRequest(http.MethodGet, "/stock/fetch-price/cache/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		// Execute handler
		err := handler(c)

		// Should return HTTP error
		assert.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		assert.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "stock code is required")
	})

	t.Run("should convert stock code to uppercase", func(t *testing.T) {
		// Create request with lowercase stock code
		req := httptest.NewRequest(http.MethodGet, "/stock/fetch-price/cache/vnd", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("stockCode")
		c.SetParamValues("vnd")

		// Execute handler
		err := handler(c)
		require.NoError(t, err)

		// Check response
		assert.Equal(t, http.StatusOK, rec.Code)
		responseBody := rec.Body.String()
		assert.True(t,
			strings.Contains(responseBody, "VND downloaded") ||
				strings.Contains(responseBody, "VND are already cached"),
			"Response should reference uppercase VND")

		// Clean up potential cache file
		cacheFile := filepath.Join("precache/stockprices", "VND.csv")
		os.Remove(cacheFile)
	})
}

func TestFetchStockPriceCache_Unit(t *testing.T) {
	// Create service without FireAnt (will fail on actual API call)
	s := &service.Service{
		Validator: util.NewValidator(),
	}

	// Setup Echo
	e := echo.New()
	e.Validator = s.Validator

	// Create handler
	handler := stock.FetchStockPriceCache(s)

	t.Run("should return error for empty stock code", func(t *testing.T) {
		// Create request without stock code
		req := httptest.NewRequest(http.MethodGet, "/stock/fetch-price/cache/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		// Execute handler
		err := handler(c)

		// Should return HTTP error
		assert.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		assert.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "stock code is required")
	})

	t.Run("should return cached message when file is recent", func(t *testing.T) {
		// Create cache directory and file
		cacheDir := "precache/stockprices"
		os.MkdirAll(cacheDir, 0755)
		cacheFile := filepath.Join(cacheDir, "TEST.csv")

		// Create a recent cache file
		file, err := os.Create(cacheFile)
		require.NoError(t, err)
		file.WriteString("Date,Open,High,Low,Close,Volume\n")
		file.WriteString("2025-06-24,100,105,95,102,1000\n")
		file.Close()

		defer os.Remove(cacheFile) // Clean up

		// Create request
		req := httptest.NewRequest(http.MethodGet, "/stock/fetch-price/cache/TEST", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("stockCode")
		c.SetParamValues("TEST")

		// Execute handler
		err = handler(c)
		require.NoError(t, err)

		// Check response
		assert.Equal(t, http.StatusOK, rec.Code)
		assert.Contains(t, rec.Body.String(), "already cached and up to date")
	})
}
