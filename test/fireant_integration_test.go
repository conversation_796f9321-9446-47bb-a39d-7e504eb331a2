package test

import (
	"context"
	"crypto/tls"
	"net/http"
	"testing"
	"time"

	"github.com/007lock/simon-homestead/external/fireant"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFireAntService_FetchStockPrices_Integration(t *testing.T) {
	// Skip integration test if running in CI or if no bearer token is provided
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create test config
	cfg, err := CreateConfig()
	require.NoError(t, err, "Failed to create config")

	// Skip test if no bearer token is configured
	if cfg.FireAnt == nil || cfg.FireAnt.BearerToken == "" {
		t.Skip("Skipping integration test: FireAnt bearer token not configured")
	}

	// Create HTTP transport
	transport := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
	}

	// Create FireAnt service
	faService := fireant.NewFireAntService(cfg, transport)

	// Test parameters
	stockCode := "VND"
	startDate := "2022-06-24"
	endDate := "2025-06-24"
	page := 0

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Test the FetchStockPrices method
	t.Run("FetchStockPrices_Success", func(t *testing.T) {
		result, err := faService.FetchStockPrices(ctx, stockCode, startDate, endDate, page)

		// Assertions
		assert.NoError(t, err, "FetchStockPrices should not return an error")
		assert.NotNil(t, result, "Result should not be nil")

		if result != nil {
			// Check that we got some data
			assert.Greater(t, len(*result), 0, "Should return at least one stock price record")

			// Check the structure of the first record if available
			if len(*result) > 0 {
				firstRecord := (*result)[0]
				assert.NotEmpty(t, firstRecord.Date, "Date should not be empty")
				assert.NotEmpty(t, firstRecord.Symbol, "Symbol should not be empty")
				assert.Equal(t, stockCode, firstRecord.Symbol, "Symbol should match requested stock code")

				// Check that price fields are reasonable (not negative)
				assert.GreaterOrEqual(t, firstRecord.PriceOpen, 0.0, "PriceOpen should be non-negative")
				assert.GreaterOrEqual(t, firstRecord.PriceHigh, 0.0, "PriceHigh should be non-negative")
				assert.GreaterOrEqual(t, firstRecord.PriceLow, 0.0, "PriceLow should be non-negative")
				assert.GreaterOrEqual(t, firstRecord.PriceClose, 0.0, "PriceClose should be non-negative")

				// Log some sample data for verification
				t.Logf("Sample record: Date=%s, Symbol=%s, Open=%.2f, High=%.2f, Low=%.2f, Close=%.2f",
					firstRecord.Date, firstRecord.Symbol, firstRecord.PriceOpen,
					firstRecord.PriceHigh, firstRecord.PriceLow, firstRecord.PriceClose)
			}
		}
	})

	// Test with different parameters
	t.Run("FetchStockPrices_DifferentDateRange", func(t *testing.T) {
		// Test with a shorter date range
		shortStartDate := "2024-01-01"
		shortEndDate := "2024-12-31"

		result, err := faService.FetchStockPrices(ctx, stockCode, shortStartDate, shortEndDate, page)

		assert.NoError(t, err, "FetchStockPrices with different date range should not return an error")
		assert.NotNil(t, result, "Result should not be nil")
	})

	// Test pagination
	t.Run("FetchStockPrices_Pagination", func(t *testing.T) {
		// Test first page
		page1Result, err := faService.FetchStockPrices(ctx, stockCode, startDate, endDate, 0)
		assert.NoError(t, err, "First page should not return an error")
		assert.NotNil(t, page1Result, "First page result should not be nil")

		// Test second page (if there's enough data)
		page2Result, err := faService.FetchStockPrices(ctx, stockCode, startDate, endDate, 1)
		assert.NoError(t, err, "Second page should not return an error")
		assert.NotNil(t, page2Result, "Second page result should not be nil")

		// If both pages have data, they should be different
		if page1Result != nil && page2Result != nil && len(*page1Result) > 0 && len(*page2Result) > 0 {
			// Compare first records to ensure they're different
			if len(*page1Result) > 0 && len(*page2Result) > 0 {
				page1First := (*page1Result)[0]
				page2First := (*page2Result)[0]
				assert.NotEqual(t, page1First.Date, page2First.Date, "Different pages should return different data")
			}
		}
	})
}

func TestFireAntService_FetchStockPrices_ErrorCases(t *testing.T) {
	// Create test config
	cfg, err := CreateConfig()
	require.NoError(t, err, "Failed to create config")

	// Create HTTP transport
	transport := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
	}

	// Create FireAnt service
	faService := fireant.NewFireAntService(cfg, transport)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Test with invalid stock code
	t.Run("FetchStockPrices_InvalidStockCode", func(t *testing.T) {
		result, err := faService.FetchStockPrices(ctx, "INVALID_STOCK", "2024-01-01", "2024-12-31", 0)

		// The API might return an empty result or an error - both are acceptable
		if err != nil {
			t.Logf("Expected error for invalid stock code: %v", err)
		} else if result != nil {
			assert.Equal(t, 0, len(*result), "Invalid stock code should return empty result")
		}
	})

	// Test with invalid date format
	t.Run("FetchStockPrices_InvalidDateFormat", func(t *testing.T) {
		result, err := faService.FetchStockPrices(ctx, "VND", "invalid-date", "2024-12-31", 0)

		// This might cause an error or return empty results
		if err == nil && result != nil {
			// If no error, result might be empty or contain unexpected data
			t.Logf("Result with invalid date format: %d records", len(*result))
		}
	})
}
