package route

import (
	apiuidaily "github.com/007lock/simon-homestead/pkg/api/ui/daily"
	apiuiportfolio "github.com/007lock/simon-homestead/pkg/api/ui/portfolio"
	"github.com/007lock/simon-homestead/pkg/service"

	"github.com/labstack/echo/v4"
)

func NewRoutePublic(s *service.Service, router *echo.Group, middwares ...echo.MiddlewareFunc) {

	public := router.Group("/resources", middwares...)
	{
		// Daily api
		public.GET("/daily", apiuidaily.UserDailyList(s))
		public.DELETE("/daily", apiuidaily.DeleteDaily(s))
		public.PUT("/daily", apiuidaily.ModifyDaily(s))

		// Portfolio api
		public.GET("/portfolio", apiuiportfolio.GetAssets(s))
		public.GET("/portfolio/watching", apiuiportfolio.GetAssetSelling(s))
		public.GET("/portfolio/fair-values", apiuiportfolio.GetAssetFairValues(s))
		public.GET("/portfolio/events", apiuiportfolio.GetEventTimeline(s))
		public.GET("/portfolio/cash-events", apiuiportfolio.GetCashEventTimeline(s))
		public.POST("/portfolio/dist", apiuiportfolio.GetAssetDistribution(s))
		public.POST("/portfolio/variance", apiuiportfolio.GetPortfolioVariance(s))
	}
}
