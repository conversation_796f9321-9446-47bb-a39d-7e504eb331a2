package route

import (
	apibotchatbot "github.com/007lock/simon-homestead/pkg/api/bot"
	apitcommunicate "github.com/007lock/simon-homestead/pkg/api/bot/communicate"
	apibotinterest "github.com/007lock/simon-homestead/pkg/api/bot/interest"
	apioptimize "github.com/007lock/simon-homestead/pkg/api/bot/optimize"
	apibotportfolio "github.com/007lock/simon-homestead/pkg/api/bot/portfolio"
	apibotscheduler "github.com/007lock/simon-homestead/pkg/api/bot/scheduler"
	apibotstock "github.com/007lock/simon-homestead/pkg/api/bot/stock"
	apifacebook "github.com/007lock/simon-homestead/pkg/api/facebook"
	apitelegram "github.com/007lock/simon-homestead/pkg/api/telegram"

	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
)

func NewRouteBot(s *service.Service, router *echo.Group) {
	// Portfolio management
	router.POST("/portfolio/long-asset", apibotportfolio.LongAsset(s))
	router.POST("/portfolio/short-asset", apibotportfolio.ShortAsset(s))
	router.GET("/portfolio/value", apibotportfolio.CalculateStockAsset(s))
	router.POST("/portfolio/market-price", apibotportfolio.FetchPricePortfolio(s))
	router.POST("/portfolio/pl", apibotportfolio.ProfitsAndLossesCreateJob(s))
	router.POST("/portfolio/pl/activity", apibotportfolio.ProfitAndLostOfActivity(s))
	router.POST("/portfolio/pl/upsert", apibotportfolio.ProfitAndLostUpsert(s))
	router.POST("/portfolio/pl/insert", apibotportfolio.ProfitAndLostInsert(s))
	router.POST("/portfolio/recently/upsert", apibotportfolio.RecentlyUpsert(s))

	// Portfolio automation
	router.GET("/portfolio/weight", apibotportfolio.CalculateWeight(s))
	router.POST("/portfolio/sale", apibotportfolio.CalculateSale(s))
	router.POST("/portfolio/var", apibotportfolio.CalculateVariance(s))
	router.POST("/portfolio/velocity", apibotportfolio.CalculateNewVelocity(s))
	router.POST("/portfolio/balance", apibotportfolio.CalculateBalance(s))

	// Stock optimization
	router.POST("/optimization/recognize", apioptimize.RecognizeStocks(s))
	router.POST("/optimization/var", apioptimize.CalculateVariance(s))
	router.POST("/optimization/new-weight", apioptimize.OptimizeStocks(s))

	// Stock dividend
	router.POST("/dividend/download/recently", apibotportfolio.DownloadPortfolioDividendRecently(s))
	router.POST("/dividend/download", apibotportfolio.DownloadStockDividends(s))
	router.POST("/dividend/aggregation", apibotportfolio.DividendAggregation(s))

	// Schedule webhook
	router.POST("/schedule/webhook", apibotscheduler.SendMessageToSubscribers(s))
	router.POST("/schedule/set", apibotscheduler.SubscribeNotification(s))
	router.POST("/schedule/command/set", apibotscheduler.SubscribeCommandNotification(s))
	router.POST("/schedule/view", apibotscheduler.SubscribeList(s))
	router.POST("/schedule/daily", apibotscheduler.SetupUserDailyList(s))

	// Chatbot section
	router.POST("/chatbot/suggest", apibotchatbot.ChatBotSuggest(s))
	router.POST("/chatbot/send/template", apibotchatbot.SendChatTemplate(s))

	// Bank interest section
	router.POST("/interest/bank", apibotinterest.FetchBankInterest(s))

	// Stock section
	router.POST("/stock/ratio", apibotstock.FilterAndSendStock(s))
	router.POST("/stock/var", apibotstock.CalculateStocksVar(s))
	router.POST("/stock/weighting", apibotstock.CalculateStocksWeight(s))
	router.POST("/stock/fetch-price", apibotstock.FetchPriceStock(s))
	router.POST("/stock/fetch-price-list", apibotstock.FetchPriceStockList(s))
	router.GET("/stock/code", apibotstock.GetStockCode(s))
	router.GET("/stock/detail", apibotstock.StockDetail(s))
	router.GET("/stock/grade", apibotstock.FetchGradeStocks(s))
	router.GET("/stock/list", apibotstock.SendStockList(s))
	router.GET("/stock/fetch-price/cache/:stockCode", apibotstock.FetchStockPriceCache(s))

	// Telegram section
	router.POST("/tele/receive", apitelegram.ReceiveMessage(s))
	router.POST("/channel/ask", apitcommunicate.AskUser(s))
	router.POST("/tele/chat-typing-action", apitelegram.SendTypingActionUser(s))

	// Facebook Messenger section
	router.GET("/facebook/webhook", apifacebook.VerifyWebhook(s))   // For webhook verification
	router.POST("/facebook/webhook", apifacebook.ReceiveMessage(s)) // For receiving messages
}
