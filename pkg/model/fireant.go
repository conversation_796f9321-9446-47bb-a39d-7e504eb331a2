package model

type FireAntVNIPrices struct {
	Date                    string  `json:"date"`
	Symbol                  string  `json:"symbol"`
	PriceHigh               float64 `json:"priceHigh"`
	PriceLow                float64 `json:"priceLow"`
	PriceOpen               float64 `json:"priceOpen"`
	PriceAverage            float64 `json:"priceAverage"`
	PriceClose              float64 `json:"priceClose"`
	PriceBasic              float64 `json:"priceBasic"`
	TotalVolume             float64 `json:"totalVolume"`
	DealVolume              float64 `json:"dealVolume"`
	PutthroughVolume        float64 `json:"putthroughVolume"`
	TotalValue              float64 `json:"totalValue"`
	PutthroughValue         float64 `json:"putthroughValue"`
	BuyForeignQuantity      float64 `json:"buyForeignQuantity"`
	BuyForeignValue         float64 `json:"buyForeignValue"`
	SellForeignQuantity     float64 `json:"sellForeignQuantity"`
	SellForeignValue        float64 `json:"sellForeignValue"`
	BuyCount                float64 `json:"buyCount"`
	BuyQuantity             float64 `json:"buyQuantity"`
	SellCount               float64 `json:"sellCount"`
	SellQuantity            float64 `json:"sellQuantity"`
	AdjRatio                float64 `json:"adjRatio"`
	CurrentForeignRoom      float64 `json:"currentForeignRoom"`
	PropTradingNetDealValue float64 `json:"propTradingNetDealValue"`
	PropTradingNetPTValue   float64 `json:"propTradingNetPTValue"`
	PropTradingNetValue     float64 `json:"propTradingNetValue"`
	Unit                    float64 `json:"unit"`
}

type FireAntStockPrices []struct {
	Date                    string  `json:"date"`
	Symbol                  string  `json:"symbol"`
	PriceHigh               float64 `json:"priceHigh"`
	PriceLow                float64 `json:"priceLow"`
	PriceOpen               float64 `json:"priceOpen"`
	PriceAverage            float64 `json:"priceAverage"`
	PriceClose              float64 `json:"priceClose"`
	PriceBasic              float64 `json:"priceBasic"`
	TotalVolume             float64 `json:"totalVolume"`
	DealVolume              float64 `json:"dealVolume"`
	PutthroughVolume        float64 `json:"putthroughVolume"`
	TotalValue              float64 `json:"totalValue"`
	PutthroughValue         float64 `json:"putthroughValue"`
	BuyForeignQuantity      float64 `json:"buyForeignQuantity"`
	BuyForeignValue         float64 `json:"buyForeignValue"`
	SellForeignQuantity     float64 `json:"sellForeignQuantity"`
	SellForeignValue        float64 `json:"sellForeignValue"`
	BuyCount                float64 `json:"buyCount"`
	BuyQuantity             float64 `json:"buyQuantity"`
	SellCount               float64 `json:"sellCount"`
	SellQuantity            float64 `json:"sellQuantity"`
	AdjRatio                float64 `json:"adjRatio"`
	CurrentForeignRoom      float64 `json:"currentForeignRoom"`
	PropTradingNetDealValue float64 `json:"propTradingNetDealValue"`
	PropTradingNetPTValue   float64 `json:"propTradingNetPTValue"`
	PropTradingNetValue     float64 `json:"propTradingNetValue"`
	Unit                    float64 `json:"unit"`
}
