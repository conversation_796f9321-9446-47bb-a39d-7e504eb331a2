app_name: Simon homestead
environment: development
port: 8011
worker_pool: 5
stock_price_cache_hour: 1
frontend_url: https://homestead.phokim.co
chat_scopes:
  - scheduler
  - financial-analysis
  - portfolio
jwt:
  secret: secret
storage:
  model: precache/bot
  stockprices: precache/stockprices
  cafefdata: precache/cafefdata
  cafefin: precache/cafefin
  vsd: precache/vsd
  sscdata: precache/sscdata
  sscfin: precache/sscfin
  ssifin: precache/ssifin
telegram:
  token:
  owner:
facebook:
  graph_url: https://graph.facebook.com/v21.0
  verify_token:
  app_secret:
  page_access_token:
redis_url: redis://:redis@localhost:6379
fireant:
  bearer_token:
ssi:
  xfiin_user_token:
database:
  url: postgres://postgres:postgres@localhost:5432/homestead_db?sslmode=disable
  migration: file://./migrations
s3:
  bucket: simon-homestead
  region: us-east-1
  key_id:
  access_key:
  internal: false
  url_public: https://s3-us-east-1.amazonaws.com/simon-homestead
template:
  email_path: template/email/*.html
  message_path: template/message/*.tpl
  routine_path: template/routine
  ssi_dict_path: template/finance
  train_path: template/pretrain
  chat_path: template/chat-model
