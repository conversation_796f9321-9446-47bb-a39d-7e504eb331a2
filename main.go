package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"text/template"
	"time"

	"github.com/007lock/simon-homestead/external/cafef"
	"github.com/007lock/simon-homestead/external/fireant"
	"github.com/007lock/simon-homestead/external/vietstock"
	"github.com/007lock/simon-homestead/pkg/chatbot"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/007lock/simon-homestead/pkg/facebook"
	"github.com/007lock/simon-homestead/pkg/filejob"
	"github.com/007lock/simon-homestead/pkg/route"
	"github.com/007lock/simon-homestead/pkg/service"
	s3Storage "github.com/007lock/simon-homestead/pkg/storage/s3"
	"github.com/007lock/simon-homestead/pkg/telegram"
	"github.com/007lock/simon-homestead/pkg/util"

	"github.com/labstack/echo/v4"
	_ "github.com/lib/pq"
	"github.com/spf13/viper"

	_ "github.com/mithrandie/csvq-driver"
	"go.uber.org/zap"
)

var ser *service.Service

func main() {
	// Logger
	rawJSON := []byte(`{
		"level": "debug",
		"encoding": "json",
		"outputPaths": ["stdout"],
		"errorOutputPaths": ["stderr"],
		"encoderConfig": {
		  "messageKey": "message",
		  "levelKey": "level",
		  "levelEncoder": "lowercase"
		}
	  }`)
	var zapCfg zap.Config
	if err := json.Unmarshal(rawJSON, &zapCfg); err != nil {
		panic(err)
	}

	zapLogger, err := zapCfg.Build()
	if err != nil {
		panic(err)
	}
	defer zapLogger.Sync()

	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		zapLogger.Info("Error reading config file from env.yaml")
		panic(err)
	}

	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	var cfg *config.Config
	err = viper.Unmarshal(&cfg)
	if err != nil {
		zapLogger.Info("Error init viper")
		panic(err)
	}
	fmt.Println(cfg.PrettyPrint())

	// Template
	tempts := template.Must(template.ParseGlob(cfg.TemplatePath.EmailPath))
	tempts = template.Must(tempts.ParseGlob(cfg.TemplatePath.MessagePath))
	renderer := util.NewTemplateRenderer(tempts)

	// JWT
	jwts := util.NewJWTService(cfg)

	// Database
	parsedURL, err := url.Parse(cfg.Database.URL)
	if err != nil {
		panic(err)
	}

	db, err := sql.Open(parsedURL.Scheme, cfg.Database.URL)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			panic(err)
		}
	}()

	// Database prices
	dbPrice, err := sql.Open("csvq", cfg.StoragePath.StockPrices)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := dbPrice.Close(); err != nil {
			panic(err)
		}
	}()

	// // Redis
	// rc, err := redis.NewRedisClient(cfg)
	// if err != nil {
	// 	panic(err)
	// }

	// dialer, err := proxy.SOCKS5("tcp", "127.0.0.1:12050", nil, proxy.Direct)
	// if err != nil {
	// 	panic(err)
	// }
	trans := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		// 	Dial: dialer.Dial,
	}

	// Telegram
	tl := telegram.NewTelegram(cfg, trans)
	// Facebook
	fb := facebook.NewFacebook(cfg, zapLogger, trans)

	// File server storage
	sst, err := s3Storage.NewS3Storage(cfg)
	if err != nil {
		panic(err)
	}

	// Vietstock
	vsSrv := vietstock.NewVietstock(cfg, zapLogger, trans)

	// Cafef service
	cff := cafef.NewCafefService(cfg, trans)

	// FireAnt service
	faSrv := fireant.NewFireAntService(cfg, trans)

	// Pacman
	pacman, err := filejob.NewPacman(cfg, db, zapLogger, trans)
	if err != nil {
		panic(err)
	}

	// Initialize chatbot
	chatBot, err := chatbot.NewMarkovStateMachine(cfg)
	if err != nil {
		panic(err)
	}

	err = pacman.ReleaseTheJobEater(chatBot)
	if err != nil {
		panic(err)
	}

	// Services
	ser = &service.Service{
		Echo:          echo.New(),
		Config:        cfg,
		Logger:        zapLogger,
		Template:      renderer,
		Validator:     util.NewValidator(),
		DB:            db,
		PriceFileDB:   dbPrice,
		ServerStorage: sst,
		Vietstock:     vsSrv,
		Cafef:         cff,
		FireAnt:       faSrv,
		Pacman:        pacman,
		Telegram:      tl,
		Facebook:      fb,
		JWTs:          jwts,
		Chatbot:       chatBot,
	}

	server := route.Init(ser)
	// Start server
	go func() {
		err = server.Serve()
		if err != nil {
			zapLogger.Fatal(err.Error())
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server with a timeout of 10 seconds.
	// Use a buffered channel to avoid missing signals as recommended for signal.Notify
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit

	// Grateful shutdown context
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := ser.Echo.Shutdown(ctx); err != nil {
		panic(err)
	}

}
