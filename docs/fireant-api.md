# FireAnt API Integration

This document describes the FireAnt service integration for fetching stock prices from the FireAnt API.

## Overview

The FireAnt service provides access to Vietnamese stock market data through the FireAnt API. The service includes methods to fetch historical stock prices with flexible parameters.

## Configuration

Add the FireAnt configuration to your `env.yaml` file:

```yaml
fireant:
  bearer_token: "your_bearer_token_here"
```

## Available Methods

### FetchStockPrices

Fetches historical stock prices for a specific stock with customizable parameters.

**Signature:**
```go
FetchStockPrices(ctx context.Context, stockCode string, startDate string, endDate string, page int) (*model.FireAntStockPrices, error)
```

**Parameters:**
- `ctx`: Context for request cancellation and timeout
- `stockCode`: Stock symbol (e.g., "VND", "VIC", "FPT")
- `startDate`: Start date in "YYYY-MM-DD" format (e.g., "2022-06-24")
- `endDate`: End date in "YYYY-MM-DD" format (e.g., "2025-06-24")
- `page`: Page number for pagination (0-based, each page contains 20 records)

**Returns:**
- `*model.FireAntStockPrices`: Slice of stock price records
- `error`: Error if the request fails

**Example Usage:**
```go
package main

import (
    "context"
    "crypto/tls"
    "fmt"
    "net/http"
    "time"

    "github.com/007lock/simon-homestead/external/fireant"
    "github.com/007lock/simon-homestead/pkg/config"
)

func main() {
    // Setup configuration and transport
    cfg := &config.Config{
        FireAnt: &config.FireAnt{
            BearerToken: "your_token_here",
        },
    }
    
    transport := &http.Transport{
        MaxIdleConns:        10,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     10 * time.Second,
        TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
    }

    // Create service
    faService := fireant.NewFireAntService(cfg, transport)

    // Fetch stock prices
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    result, err := faService.FetchStockPrices(ctx, "VND", "2022-06-24", "2025-06-24", 0)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }

    fmt.Printf("Fetched %d records\n", len(*result))
    for _, record := range *result {
        fmt.Printf("Date: %s, Symbol: %s, Close: %.2f\n", 
            record.Date, record.Symbol, record.PriceClose)
    }
}
```

### FetchVNIStockPrices (Legacy)

Fetches VN-INDEX historical data with predefined parameters.

**Signature:**
```go
FetchVNIStockPrices(ctx context.Context, page int) error
```

This method is maintained for backward compatibility and saves data to CSV files.

## Data Model

The `FireAntStockPrices` type contains the following fields:

```go
type FireAntStockPrices []struct {
    Date                    string  `json:"date"`
    Symbol                  string  `json:"symbol"`
    PriceHigh               float64 `json:"priceHigh"`
    PriceLow                float64 `json:"priceLow"`
    PriceOpen               float64 `json:"priceOpen"`
    PriceAverage            float64 `json:"priceAverage"`
    PriceClose              float64 `json:"priceClose"`
    PriceBasic              float64 `json:"priceBasic"`
    TotalVolume             float64 `json:"totalVolume"`
    DealVolume              float64 `json:"dealVolume"`
    PutthroughVolume        float64 `json:"putthroughVolume"`
    TotalValue              float64 `json:"totalValue"`
    // ... additional fields for foreign trading, buy/sell counts, etc.
}
```

## API Endpoint

The service calls the FireAnt REST API:
```
GET https://restv2.fireant.vn/symbols/{stockCode}/historical-quotes?startDate={startDate}&endDate={endDate}&offset={offset}&limit=20
```

## Error Handling

The service handles various error conditions:
- Network timeouts (returns `constants.RequestError.SHOULD_RETRY`)
- Invalid stock codes (may return empty results)
- Authentication errors (when bearer token is invalid)
- JSON parsing errors

## Testing

Integration tests are available in `test/fireant_integration_test.go`. Run tests with:

```bash
# Run all tests
go test -v ./test

# Run only FireAnt integration tests (requires bearer token)
go test -v ./test -run TestFireAntService_FetchStockPrices_Integration

# Run error case tests
go test -v ./test -run TestFireAntService_FetchStockPrices_ErrorCases
```

## Rate Limiting

Be mindful of API rate limits when making multiple requests. Consider implementing delays between requests for bulk operations.

## Security

- Store bearer tokens securely
- Use environment variables or secure configuration management
- Never commit bearer tokens to version control
