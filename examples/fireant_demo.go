//go:build ignore
// +build ignore

package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"time"

	"github.com/007lock/simon-homestead/external/fireant"
	"github.com/007lock/simon-homestead/pkg/config"
	"github.com/spf13/viper"
)

func main() {
	// Configuration
	viper.SetConfigName("env")
	viper.AddConfigPath(".")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		fmt.Printf("Error reading config file: %v\n", err)
		return
	}

	var cfg *config.Config
	err := viper.Unmarshal(&cfg)
	if err != nil {
		fmt.Printf("Error unmarshaling config: %v\n", err)
		return
	}

	// Create HTTP transport
	transport := &http.Transport{
		MaxIdleConns:        10,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     10 * time.Second,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
	}

	// Create FireAnt service
	faService := fireant.NewFireAntService(cfg, transport)

	// Example parameters
	stockCode := "VND"
	startDate := "2022-06-24"
	endDate := "2025-06-24"
	page := 0

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	fmt.Printf("Fetching stock prices for %s from %s to %s (page %d)...\n",
		stockCode, startDate, endDate, page)

	// Call the new FetchStockPrices method
	result, err := faService.FetchStockPrices(ctx, stockCode, startDate, endDate, page)
	if err != nil {
		fmt.Printf("Error fetching stock prices: %v\n", err)
		return
	}

	if result == nil {
		fmt.Println("No data returned")
		return
	}

	fmt.Printf("Successfully fetched %d stock price records\n", len(*result))

	// Display first few records
	displayCount := 5
	if len(*result) < displayCount {
		displayCount = len(*result)
	}

	fmt.Printf("\nFirst %d records:\n", displayCount)
	fmt.Println("Date\t\t\tSymbol\tOpen\tHigh\tLow\tClose\tVolume")
	fmt.Println("-------------------------------------------------------------------")

	for i := 0; i < displayCount; i++ {
		record := (*result)[i]
		fmt.Printf("%s\t%s\t%.2f\t%.2f\t%.2f\t%.2f\t%.0f\n",
			record.Date,
			record.Symbol,
			record.PriceOpen,
			record.PriceHigh,
			record.PriceLow,
			record.PriceClose,
			record.TotalVolume)
	}

	// Example of fetching multiple pages
	fmt.Println("\nFetching second page...")
	page2Result, err := faService.FetchStockPrices(ctx, stockCode, startDate, endDate, 1)
	if err != nil {
		fmt.Printf("Error fetching second page: %v\n", err)
	} else if page2Result != nil {
		fmt.Printf("Second page contains %d records\n", len(*page2Result))
	}
}
